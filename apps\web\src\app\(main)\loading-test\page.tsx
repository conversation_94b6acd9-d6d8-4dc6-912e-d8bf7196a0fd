'use client';

import React from 'react';
import { useLoading } from '@/components/loading';

/**
 * Loading Screen Test Page
 * 
 * This page allows testing the Facebook Meta-style loading screen functionality.
 * Use this to verify that the loading screen works correctly.
 */
export default function LoadingTestPage() {
  const { isLoading, progress, showLoadingScreen, hideLoadingScreen, setProgress } = useLoading();

  const handleShowLoading = () => {
    showLoadingScreen();
    
    // Simulate loading progress
    let currentProgress = 0;
    const interval = setInterval(() => {
      currentProgress += 10;
      setProgress(currentProgress);
      
      if (currentProgress >= 100) {
        clearInterval(interval);
        setTimeout(() => {
          hideLoadingScreen();
        }, 1000);
      }
    }, 300);
  };

  const handleHideLoading = () => {
    hideLoadingScreen();
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">
          Facebook Meta Loading Screen Test
        </h1>
        
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">
              Current Loading State
            </h2>
            <div className="space-y-2">
              <p><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
              <p><strong>Progress:</strong> {progress}%</p>
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Test Controls
            </h2>
            
            <div className="flex gap-4">
              <button
                onClick={handleShowLoading}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                disabled={isLoading}
              >
                Show Loading Screen
              </button>
              
              <button
                onClick={handleHideLoading}
                className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                disabled={!isLoading}
              >
                Hide Loading Screen
              </button>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-yellow-900 mb-2">
              How to Test Full Page Loading
            </h2>
            <div className="space-y-2 text-yellow-800">
              <p>1. <strong>Full Page Reload:</strong> Press Ctrl+F5 or Cmd+Shift+R to see the loading screen on page reload</p>
              <p>2. <strong>Direct URL Visit:</strong> Open a new tab and navigate directly to any URL to see the loading screen</p>
              <p>3. <strong>Authentication Flow:</strong> Sign out and sign back in to see the loading screen during authentication</p>
              <p>4. <strong>SPA Navigation:</strong> Use the sidebar or navigation links - loading screen should NOT appear</p>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h2 className="text-lg font-semibold text-green-900 mb-2">
              Features Implemented
            </h2>
            <ul className="space-y-1 text-green-800">
              <li>✅ Facebook Meta-style animations and design</li>
              <li>✅ Full-screen overlay with gradient background</li>
              <li>✅ Animated logo with pulsing rings</li>
              <li>✅ Progress bar with shimmer effect</li>
              <li>✅ Bouncing dots animation</li>
              <li>✅ Floating background circles</li>
              <li>✅ Responsive design for mobile and desktop</li>
              <li>✅ Accessibility support (respects reduced motion)</li>
              <li>✅ Integration with authentication flow</li>
              <li>✅ Only shows on full page reloads, not SPA navigation</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
