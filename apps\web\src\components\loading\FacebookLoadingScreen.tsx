'use client';

import React from 'react';

interface FacebookLoadingScreenProps {
  isVisible: boolean;
  progress?: number;
}

/**
 * Facebook Meta-style Full Page Loading Screen
 * 
 * Features:
 * - Full-screen overlay that covers entire viewport
 * - Facebook Meta-style logo animation
 * - Smooth progress indicator
 * - Only shows on full page reloads, not SPA navigation
 * - Integrates with authentication flow
 */
export function FacebookLoadingScreen({ isVisible, progress = 0 }: FacebookLoadingScreenProps) {
  if (!isVisible) return null;

  return (
    <div className="facebook-loading-screen">
      {/* Full Screen Overlay */}
      <div className="facebook-loading-overlay">
        {/* Main Content Container */}
        <div className="facebook-loading-content">
          {/* Meta Logo Animation */}
          <div className="facebook-logo-container">
            <div className="facebook-logo">
              <svg
                width="64"
                height="64"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="facebook-logo-svg"
              >
                <path
                  d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"
                  fill="currentColor"
                />
              </svg>
            </div>
            
            {/* Pulsing Ring Animation */}
            <div className="facebook-pulse-ring"></div>
            <div className="facebook-pulse-ring facebook-pulse-ring-delay"></div>
          </div>

          {/* Loading Text */}
          <div className="facebook-loading-text">
            <h2>Grandline Maritime</h2>
            <p>Loading your experience...</p>
          </div>

          {/* Progress Bar */}
          <div className="facebook-progress-container">
            <div className="facebook-progress-bar">
              <div 
                className="facebook-progress-fill"
                style={{ width: `${Math.min(progress, 100)}%` }}
              ></div>
            </div>
            <div className="facebook-progress-dots">
              <div className="facebook-dot facebook-dot-1"></div>
              <div className="facebook-dot facebook-dot-2"></div>
              <div className="facebook-dot facebook-dot-3"></div>
            </div>
          </div>
        </div>

        {/* Background Pattern */}
        <div className="facebook-bg-pattern">
          <div className="facebook-bg-circle facebook-bg-circle-1"></div>
          <div className="facebook-bg-circle facebook-bg-circle-2"></div>
          <div className="facebook-bg-circle facebook-bg-circle-3"></div>
        </div>
      </div>
    </div>
  );
}

export default FacebookLoadingScreen;
