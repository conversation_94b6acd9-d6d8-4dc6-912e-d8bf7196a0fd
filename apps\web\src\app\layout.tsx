import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
// import { ReduxProvider } from "@encreasl/redux"; // Removed - no authentication needed
import { LoadingScreenWrapper } from "@/components/loading";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Grandline Maritime Training and Development Center Inc",
  description: "Transform your ecommerce business with data-driven marketing strategies. We help online stores increase sales, optimize conversions, and scale profitably.",
  keywords: "ecommerce marketing, digital marketing agency, online store marketing, conversion optimization, ecommerce growth",
  authors: [{ name: "Calsiter Team" }],
  openGraph: {
    title: "Grandline Maritime Training and Development Center Inc",
    description: "Transform your ecommerce business with data-driven marketing strategies.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <LoadingScreenWrapper>
          {children}
        </LoadingScreenWrapper>
      </body>
    </html>
  );
}
